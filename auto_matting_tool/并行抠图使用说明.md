# 🚀 并行抠图工具使用说明

## 🎯 核心特性

**解决了你提到的问题**：
- ✅ **处理时同时输入** - 单张图片处理过程中可以立即输入下一个文件
- ✅ **无缝连接** - 避免输入时机不对导致的输入丢失
- ✅ **真正的连续处理** - 处理完立即开始下一个，不浪费时间

## 🔥 工作原理

### **传统方式的问题**：
```
处理图片 → 等待 → 处理完成 → 弹出输入框 → 输入下一个
❌ 如果在"等待"时输入，会丢失
❌ 时机难把握
```

### **并行抠图的解决方案**：
```
开始处理图片 → 立即显示输入框 → 可以马上输入下一个
✅ 处理和输入同时进行
✅ 输入会被保存，处理完立即开始下一个
```

## 🚀 启动方式

### 方式1: 双击启动
```bash
双击 "并行抠图.command" 文件
```

### 方式2: 终端启动
```bash
cd auto_matting_tool
python3 并行抠图.py
```

## 💡 使用流程

### **单张图片处理**：
1. 拖拽第一张图片到终端
2. 程序开始处理，**同时显示输入框**
3. 立即拖拽第二张图片（不用等第一张处理完）
4. 第一张处理完后，自动开始处理第二张
5. 继续拖拽第三张...

### **实际体验**：
```bash
>>> /path/to/image1.jpg
📁 输入: image1.jpg
⏳ 处理中... (可以立即输入下一个文件)

==================================================
🚀 可以立即输入下一个文件路径:
💡 提示: 直接拖拽文件/文件夹，或输入命令
==================================================
下一个>>> /path/to/image2.jpg
✅ 已接收: /path/to/image2.jpg
⏳ 等待当前处理完成...

✅ 处理完成!
🔄 处理排队输入: /path/to/image2.jpg
📁 输入: image2.jpg
...
```

## 🎮 操作技巧

### **快速连续处理**：
1. 拖拽图片1 → 立即拖拽图片2 → 立即拖拽图片3
2. 程序会按顺序自动处理，无需等待

### **混合处理**：
```bash
# 先处理单张图片
拖拽 image1.jpg

# 处理过程中输入批量文件夹
下一个>>> /path/to/folder

# 程序会先完成单张，再批量处理文件夹
```

### **快捷设置**：
```bash
# 在处理过程中也可以输入命令
下一个>>> preset 高质量
下一个>>> model sam
下一个>>> /path/to/next_image.jpg
```

## ⚡ 快捷命令

所有原有的快捷命令都支持：

| 命令 | 示例 | 说明 |
|------|------|------|
| `model <名称>` | `model rmbg-1.4` | 快速切换模型 |
| `padding <数字>` | `padding 20` | 设置边缘像素 |
| `format <格式>` | `format png` | 设置输出格式 |
| `preset <名称>` | `preset 高质量` | 应用预设配置 |
| `config` | `config` | 查看当前设置 |
| `settings` | `settings` | 打开完整设置 |

## 🔄 处理模式

### **单张图片模式**：
- 🔥 **支持并行输入**
- 处理过程中可以输入下一个文件
- 真正的无缝连续处理

### **批量处理模式**：
- 📁 **传统模式**
- 批量处理不需要并行输入
- 处理完成后等待下一个输入

## 💡 使用建议

### **最佳工作流**：
```bash
# 1. 启动工具
python3 并行抠图.py

# 2. 设置预设
preset 高质量

# 3. 开始连续处理
拖拽图片1 → 立即拖拽图片2 → 立即拖拽图片3...
```

### **效率提升**：
- ⚡ **节省等待时间** - 处理时就能输入下一个
- 🔄 **真正连续** - 无缝衔接，不中断
- 🎯 **精确控制** - 想处理多少就排队多少

## 🚨 注意事项

1. **输入队列**：程序会按顺序处理排队的输入
2. **命令优先**：快捷命令会立即执行，不会排队
3. **批量处理**：批量处理时不会启用并行输入
4. **错误处理**：如果输入的文件不存在，会跳过并处理下一个

## 🆚 对比原版

| 特性 | 原版连续抠图 | 并行抠图 |
|------|-------------|----------|
| 连续处理 | ✅ | ✅ |
| 快捷命令 | ✅ | ✅ |
| 预设配置 | ✅ | ✅ |
| 处理时输入 | ❌ | ✅ |
| 无缝衔接 | ❌ | ✅ |
| 输入队列 | ❌ | ✅ |

## 🎯 适用场景

### **完美适合**：
- 🖼️ **大量单张图片处理**
- ⚡ **需要快速连续处理**
- 🔄 **不想等待的用户**
- 💼 **专业批量工作**

### **使用原版的情况**：
- 📁 **主要是批量处理文件夹**
- 🐌 **不介意等待的用户**
- 🔧 **只需要基础功能**

---

## 🎉 总结

**并行抠图工具**完美解决了你提到的问题：
- ✅ **处理时可以立即输入下一个文件**
- ✅ **不会因为时机问题丢失输入**
- ✅ **真正的无缝连续处理体验**

现在你可以：
1. 拖拽图片1
2. 立即拖拽图片2（不用等）
3. 立即拖拽图片3（不用等）
4. 程序自动按顺序处理，完全无缝！

**享受真正高效的连续抠图体验！** 🚀
