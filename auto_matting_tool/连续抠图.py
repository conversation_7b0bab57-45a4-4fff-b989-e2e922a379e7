#!/usr/bin/env python3
"""
连续抠图工具 - 无需每次询问，支持拖拽处理
专为快速连续处理设计，使用 RMBG-1.4 模型
"""

import os
import sys
import glob
import threading
import queue
import time
from config import load_config, get_output_path, show_current_config, modify_config, show_presets, apply_preset

def clean_path(path):
    """清理路径中的特殊字符"""
    path = path.strip()
    if path.startswith('"') and path.endswith('"'):
        path = path[1:-1]  # 去掉首尾引号
    
    # 处理转义字符
    path = path.replace('\\ ', ' ')  # 处理转义的空格
    path = path.replace('\\(', '(')  # 处理转义的左括号
    path = path.replace('\\)', ')')  # 处理转义的右括号
    return path

def process_single_image(input_path, input_queue=None):
    """处理单张图片"""
    config = load_config()

    # 检查文件是否存在
    if not os.path.exists(input_path):
        print(f"❌ 文件不存在: {input_path}")
        return False

    # 生成输出路径
    output_path = get_output_path(input_path, config)

    print(f"📁 输入: {os.path.basename(input_path)}")
    print(f"📁 输出: {output_path}")
    print(f"🎛️ 设置: {config['edge_padding']}像素边缘 | {config['model']}模型")

    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))

    cmd = f"cd '{script_dir}' && python3 auto_matting.py -i '{input_path}' -o '{output_path}' -p {config['edge_padding']} -m {config['model']}"

    if config['show_progress']:
        print("⏳ 处理中...")

    # 如果有输入队列，启动并行输入
    if input_queue is not None:
        input_thread = threading.Thread(target=parallel_input_handler, args=(input_queue,))
        input_thread.daemon = True
        input_thread.start()
        print("💡 处理过程中可以输入下一个文件路径:")

    result = os.system(cmd)

    if result == 0:
        print("✅ 处理完成!")
        if config['auto_open_result']:
            import subprocess
            subprocess.run(['open', os.path.dirname(output_path)])
        return True
    else:
        print("❌ 处理失败")
        return False

def parallel_input_handler(input_queue):
    """并行输入处理器"""
    try:
        print("📎 下一个文件/文件夹路径 (或命令):")
        user_input = input(">>> ").strip()
        if user_input:
            input_queue.put(user_input)
            print(f"✅ 已接收输入: {user_input[:50]}{'...' if len(user_input) > 50 else ''}")
            print("⏳ 等待当前处理完成...")
    except (EOFError, KeyboardInterrupt):
        pass
    except Exception as e:
        print(f"⚠️ 输入处理异常: {e}")

def process_batch(input_path):
    """批量处理"""
    config = load_config()
    
    # 检查目录是否存在
    if not os.path.exists(input_path):
        print(f"❌ 目录不存在: {input_path}")
        return False
    
    # 检查是否有图片文件
    supported_formats = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp', 
                        '*.JPG', '*.JPEG', '*.PNG', '*.BMP', '*.TIFF', '*.WEBP']
    image_files = []
    for pattern in supported_formats:
        image_files.extend(glob.glob(os.path.join(input_path, pattern)))
    
    if not image_files:
        print(f"❌ 在目录中没有找到支持的图片文件: {input_path}")
        print("💡 支持的格式: jpg, jpeg, png, bmp, tiff, webp")
        return False
    
    print(f"📊 找到 {len(image_files)} 张图片")
    
    # 生成输出路径
    if config['save_location'] == 'same_folder':
        output_path = f"{input_path}_抠图结果"
    else:
        custom_path = config['custom_save_path']
        if custom_path and os.path.exists(custom_path):
            folder_name = os.path.basename(input_path)
            output_path = os.path.join(custom_path, f"{folder_name}_抠图结果")
        else:
            output_path = f"{input_path}_抠图结果"
    
    print(f"📁 输出到: {output_path}")
    print(f"🎛️ 设置: {config['edge_padding']}像素边缘 | RMBG-1.4模型")
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    cmd = f"cd '{script_dir}' && python3 auto_matting.py -i '{input_path}' -o '{output_path}' -p {config['edge_padding']} -m {config['model']}"

    if config['show_progress']:
        print("⏳ 批量处理中...")

    result = os.system(cmd)

    if result == 0:
        print("✅ 批量处理完成!")
        if config['auto_open_result']:
            import subprocess
            subprocess.run(['open', output_path])
        return True
    else:
        print("❌ 处理失败")
        return False

def show_help():
    """显示帮助信息"""
    print("\n📖 快捷命令:")
    print("=" * 50)
    print("🔧 settings/set/s    - 打开完整设置菜单")
    print("📊 config/cfg/c      - 查看当前设置")
    print("❓ help/h/?          - 显示此帮助")
    print("🚪 quit/exit/q       - 退出程序")
    print()
    print("⚡ 快捷设置:")
    print("🎯 model <名称>      - 快速切换模型")
    print("📐 padding <数字>    - 快速设置边缘像素")
    print("💾 format <格式>     - 快速设置输出格式")
    print("📁 location <位置>   - 快速设置保存位置")
    print("🎨 preset <名称>     - 应用预设配置")
    print("📋 presets          - 查看所有预设")
    print()
    print("💡 示例:")
    print("  model rmbg-1.4     - 切换到RMBG-1.4模型")
    print("  padding 20         - 设置边缘为20像素")
    print("  format png         - 设置输出为PNG格式")
    print("  location same      - 保存到同目录")
    print("  preset 高质量      - 应用高质量预设")
    print("  presets            - 查看所有预设")
    print("=" * 50)
    print("💡 或者直接拖拽图片/文件夹进行处理")

def handle_command(command):
    """处理快捷命令"""
    parts = command.lower().strip().split()
    cmd = parts[0] if parts else ""

    # 基础命令
    if cmd in ['settings', 'set', 's']:
        print("\n🔧 打开设置...")
        modify_config()
        return True
    elif cmd in ['config', 'cfg', 'c']:
        show_current_config()
        return True
    elif cmd in ['help', 'h', '?']:
        show_help()
        return True
    elif cmd in ['quit', 'exit', 'q']:
        print("👋 再见!")
        sys.exit(0)

    # 快捷设置命令
    elif cmd == 'model' and len(parts) == 2:
        return quick_set_model(parts[1])
    elif cmd == 'padding' and len(parts) == 2:
        return quick_set_padding(parts[1])
    elif cmd == 'format' and len(parts) == 2:
        return quick_set_format(parts[1])
    elif cmd == 'location' and len(parts) == 2:
        return quick_set_location(parts[1])
    elif cmd == 'preset' and len(parts) == 2:
        return quick_apply_preset(parts[1])
    elif cmd == 'presets':
        show_presets()
        return True
    else:
        return False

def quick_set_model(model_name):
    """快速设置模型"""
    from config import save_config

    models = {
        'u2net': 'u2net',
        'rmbg': 'rmbg-1.4',
        'rmbg-1.4': 'rmbg-1.4',
        'isnet': 'isnet-general-use',
        'sam': 'sam'
    }

    if model_name in models:
        config = load_config()
        config['model'] = models[model_name]
        if save_config(config):
            print(f"✅ 模型已切换为: {models[model_name]}")
        else:
            print("❌ 设置保存失败")
        return True
    else:
        print(f"❌ 不支持的模型: {model_name}")
        print("💡 支持的模型: u2net, rmbg-1.4, isnet, sam")
        return True

def quick_set_padding(padding_str):
    """快速设置边缘像素"""
    from config import save_config

    try:
        padding = int(padding_str)
        if 0 <= padding <= 100:
            config = load_config()
            config['edge_padding'] = padding
            if save_config(config):
                print(f"✅ 边缘像素已设置为: {padding}")
            else:
                print("❌ 设置保存失败")
        else:
            print("❌ 边缘像素必须在0-100之间")
    except ValueError:
        print("❌ 请输入有效的数字")
    return True

def quick_set_format(format_name):
    """快速设置输出格式"""
    from config import save_config

    formats = {'png': 'png', 'jpg': 'jpg', 'jpeg': 'jpg'}

    if format_name in formats:
        config = load_config()
        config['output_format'] = formats[format_name]
        if save_config(config):
            print(f"✅ 输出格式已设置为: {formats[format_name].upper()}")
        else:
            print("❌ 设置保存失败")
    else:
        print(f"❌ 不支持的格式: {format_name}")
        print("💡 支持的格式: png, jpg")
    return True

def quick_set_location(location):
    """快速设置保存位置"""
    from config import save_config

    locations = {'same': 'same_folder', 'custom': 'custom'}

    if location in locations:
        config = load_config()
        config['save_location'] = locations[location]
        if save_config(config):
            if location == 'same':
                print("✅ 保存位置已设置为: 原图片同目录")
            else:
                print("✅ 保存位置已设置为: 自定义目录")
                print("💡 请使用 'settings' 命令设置具体的自定义目录")
        else:
            print("❌ 设置保存失败")
    else:
        print(f"❌ 不支持的位置: {location}")
        print("💡 支持的位置: same (同目录), custom (自定义)")
    return True

def quick_apply_preset(preset_name):
    """快速应用预设"""
    presets = {"快速": "快速", "高质量": "高质量", "最强": "最强", "批量": "批量",
               "fast": "快速", "quality": "高质量", "best": "最强", "batch": "批量"}

    preset_key = presets.get(preset_name, preset_name)

    if apply_preset(preset_key):
        print(f"✅ 已应用预设: {preset_key}")
        print("💡 输入 'config' 查看当前设置")
    else:
        print(f"❌ 预设不存在: {preset_name}")
        print("💡 输入 'presets' 查看所有可用预设")
    return True

def main():
    print("🚀 连续抠图工具 - 并行输入版")
    print("=" * 50)
    print("💡 使用说明:")
    print("  • 直接拖拽图片或文件夹到终端")
    print("  • 单张图片处理时可立即输入下一个文件")
    print("  • 输入 'settings' 打开设置")
    print("  • 输入 'help' 查看更多命令")
    print("  • 按 Ctrl+C 或输入 'quit' 退出")
    print("=" * 50)

    # 显示当前配置
    config = load_config()
    print(f"⚙️ 当前设置: {config['edge_padding']}像素边缘 | {config['model'].upper()}模型 | {config['output_format'].upper()}格式 | 保存到{'同目录' if config['save_location'] == 'same_folder' else '自定义目录'}")
    print()

    # 创建输入队列用于并行输入
    input_queue = queue.Queue()

    try:
        while True:
            # 检查队列中是否有待处理的输入
            if not input_queue.empty():
                user_input = input_queue.get().strip()
            else:
                print("📎 请拖拽文件/文件夹，或输入命令:")
                user_input = input(">>> ").strip()

            if not user_input:
                print("❌ 输入不能为空")
                continue

            # 检查是否是快捷命令
            if handle_command(user_input):
                print()
                continue

            # 处理为文件路径
            input_path = clean_path(user_input)
            print(f"📝 处理后的路径: {input_path}")

            # 判断是文件还是目录
            if os.path.isfile(input_path):
                print("🖼️ 检测到单张图片")
                # 单张图片处理时启用并行输入
                process_single_image(input_path, input_queue)
            elif os.path.isdir(input_path):
                print("📁 检测到文件夹")
                # 批量处理不需要并行输入
                process_batch(input_path)
            else:
                print("❌ 路径不存在或不是有效的文件/文件夹")
                print("💡 提示: 输入 'help' 查看可用命令")
                continue

            print("\n" + "="*50)
            print("✅ 操作完成！")

            # 检查是否有排队的输入
            if not input_queue.empty():
                print("🔄 检测到排队输入，立即处理下一个...")
                continue
            else:
                print("💡 可以继续拖拽下一个文件或输入命令...")
                print()

    except KeyboardInterrupt:
        print("\n👋 再见!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        print("请重新启动程序")

if __name__ == "__main__":
    main()
