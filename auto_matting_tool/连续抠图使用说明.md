# 🚀 连续抠图工具使用说明

## 📖 简介

连续抠图工具是一个专为快速连续处理设计的智能抠图工具，支持：
- 🔄 **无缝连续处理** - 处理完自动等待下一个文件
- 🎯 **智能识别** - 自动判断单文件还是批量处理
- ⚡ **快捷命令** - 一键切换设置，无需进入复杂菜单
- 🎨 **预设配置** - 内置多种场景预设，一键应用

## 🚀 启动方式

### 方式1: 双击启动
```bash
双击 "连续抠图.command" 文件
```

### 方式2: 终端启动
```bash
cd auto_matting_tool
python3 连续抠图.py
```

## 💡 基本使用

### 1. 处理单张图片
- 直接拖拽图片文件到终端
- 或输入图片完整路径

### 2. 批量处理
- 直接拖拽文件夹到终端
- 或输入文件夹完整路径

### 3. 连续处理
- 处理完成后，直接拖拽下一个文件/文件夹
- 无需重新启动程序

## ⚡ 快捷命令

### 基础命令
| 命令 | 说明 |
|------|------|
| `help` | 显示帮助信息 |
| `config` | 查看当前设置 |
| `settings` | 打开完整设置菜单 |
| `quit` | 退出程序 |

### 快捷设置
| 命令 | 示例 | 说明 |
|------|------|------|
| `model <名称>` | `model rmbg-1.4` | 快速切换模型 |
| `padding <数字>` | `padding 20` | 设置边缘像素 |
| `format <格式>` | `format png` | 设置输出格式 |
| `location <位置>` | `location same` | 设置保存位置 |
| `preset <名称>` | `preset 高质量` | 应用预设配置 |
| `presets` | `presets` | 查看所有预设 |

## 🎨 预设配置

### 快速预设
- **模型**: u2net (速度最快)
- **边缘**: 10像素
- **格式**: JPG (文件较小)
- **适用**: 快速预览、大量图片

### 高质量预设
- **模型**: rmbg-1.4 (推荐)
- **边缘**: 20像素
- **格式**: PNG (支持透明)
- **适用**: 日常使用、高质量需求

### 最强预设
- **模型**: sam (Meta最强技术)
- **边缘**: 25像素
- **格式**: PNG
- **适用**: 复杂场景、最高质量

### 批量预设
- **模型**: rmbg-1.4
- **边缘**: 15像素
- **格式**: JPG
- **特点**: 自动打开结果、创建子文件夹

## 🔧 完整设置

输入 `settings` 进入完整设置菜单：

### 1. 📐 基础设置
- **边缘像素**: 0-100像素，控制抠图边缘预留
- **模型选择**: u2net、rmbg-1.4、isnet、sam

### 2. 💾 保存设置
- **保存位置**: 同目录 / 自定义目录
- **文件名格式**: 自定义命名规则
- **输出格式**: PNG (透明) / JPG (较小)
- **JPG质量**: 1-100% (仅JPG格式)

### 3. 📁 批量处理设置
- **创建子文件夹**: 批量处理时是否创建专门文件夹
- **覆盖文件**: 是否覆盖已存在的文件

### 4. 🖥️ 显示设置
- **显示进度**: 是否显示处理进度信息
- **自动打开**: 处理完是否自动打开结果文件夹

### 5. 📏 输出尺寸设置
- **调整尺寸**: 是否限制输出图片尺寸
- **最大尺寸**: 设置最大宽度和高度

## 💡 使用技巧

### 1. 快速切换场景
```bash
# 处理产品图片
preset 高质量

# 批量处理大量图片
preset 批量

# 处理复杂场景
preset 最强
```

### 2. 临时调整设置
```bash
# 临时增加边缘
padding 30

# 临时切换格式
format jpg

# 查看当前设置
config
```

### 3. 连续处理不同类型
```bash
# 先处理单张高质量图片
preset 高质量
[拖拽单张图片]

# 再批量处理一堆图片
preset 批量
[拖拽文件夹]
```

## 🚨 注意事项

1. **路径问题**: 如果路径包含空格或特殊字符，程序会自动处理
2. **模型下载**: 首次使用某个模型时会自动下载，需要网络连接
3. **内存使用**: SAM模型较大，处理时占用内存较多
4. **文件覆盖**: 默认不覆盖已存在文件，可在设置中修改

## 🔄 常见问题

### Q: 批量处理没有效果？
A: 检查文件夹中是否包含支持的图片格式 (jpg, png, bmp, tiff, webp)

### Q: 处理速度很慢？
A: 尝试切换到更快的模型：`model u2net`

### Q: 想要最佳效果？
A: 使用高质量预设：`preset 高质量`

### Q: 如何重置所有设置？
A: 进入设置菜单 (`settings`) 选择重置选项

## 🎯 推荐工作流

### 日常使用
1. 启动工具：`python3 连续抠图.py`
2. 应用预设：`preset 高质量`
3. 拖拽处理：直接拖拽图片/文件夹
4. 连续处理：处理完继续拖拽下一个

### 批量处理
1. 应用批量预设：`preset 批量`
2. 拖拽文件夹进行批量处理
3. 自动打开结果文件夹查看

### 高质量处理
1. 应用最强预设：`preset 最强`
2. 处理复杂场景图片
3. 获得最佳抠图效果

---

🎉 **享受高效的连续抠图体验！**
